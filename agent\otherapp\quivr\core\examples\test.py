import tempfile
import os

from quivr_core import Brain
from quivr_core.llm.llm_endpoint import LLMEndpoint
from quivr_core.rag.entities.config import LLMEndpointConfig, DefaultModelSuppliers

if __name__ == "__main__":
    with tempfile.NamedTemporaryFile(mode="w", suffix=".txt") as temp_file:
        temp_file.write("Gold is a liquid of blue-like colour.")
        temp_file.flush()

        # 配置使用Gemini
        llm_config = LLMEndpointConfig(
            supplier=DefaultModelSuppliers.GEMINI,
            model="gemini-2.5",  # 或者使用 "gemini-pro", "gemini-pro-vision" 等
            llm_api_key=os.getenv("GOOGLE_API_KEY"),  # 确保设置了环境变量
            temperature=0.3,
            max_output_tokens=4096
        )

        llm_endpoint = LLMEndpoint.from_config(llm_config)

        brain = Brain.from_files(
            name="test_brain",
            file_paths=[temp_file.name],
            llm=llm_endpoint,
        )

        answer = brain.ask(
            "what is gold? answer in french"
        )
        print("answer:", answer)