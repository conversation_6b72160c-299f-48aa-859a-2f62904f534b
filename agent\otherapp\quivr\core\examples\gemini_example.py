import os
import tempfile
from quivr_core import Brain
from quivr_core.llm.llm_endpoint import LLMEndpoint
from quivr_core.rag.entities.config import LLMEndpointConfig, DefaultModelSuppliers

def create_gemini_brain():
    """创建使用Gemini的Brain实例"""
    
    # 检查API密钥
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError("请设置GOOGLE_API_KEY环境变量")
    
    # 配置Gemini LLM
    llm_config = LLMEndpointConfig(
        supplier=DefaultModelSuppliers.GEMINI,
        model="gemini-2.5",  # 可选: gemini-pro, gemini-pro-vision
        llm_api_key=api_key,
        temperature=0.3,
        max_output_tokens=4096,
        max_context_tokens=128000
    )
    
    # 创建LLM端点
    llm_endpoint = LLMEndpoint.from_config(llm_config)
    
    # 创建临时文件作为知识库
    with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as temp_file:
        temp_file.write("""
        Quivr是一个开源的RAG (Retrieval Augmented Generation) 框架。
        它允许用户上传文档并与之对话。
        Quivr支持多种文件格式，包括PDF、文本文件、图片等。
        它使用向量数据库来存储和检索文档内容。
        """)
        temp_file.flush()
        temp_file_path = temp_file.name
    
    try:
        # 创建Brain实例
        brain = Brain.from_files(
            name="gemini_brain",
            file_paths=[temp_file_path],
            llm=llm_endpoint,
        )
        
        return brain
    finally:
        # 清理临时文件
        os.unlink(temp_file_path)

def main():
    """主函数"""
    try:
        # 创建使用Gemini的Brain
        brain = create_gemini_brain()
        
        print("🧠 Gemini Brain 创建成功!")
        print("📊 Brain信息:")
        brain.print_info()
        
        # 测试问答
        questions = [
            "什么是Quivr？",
            "Quivr支持哪些文件格式？",
            "请用英文解释RAG是什么？"
        ]
        
        for question in questions:
            print(f"\n❓ 问题: {question}")
            answer = brain.ask(question)
            print(f"🤖 回答: {answer.answer}")
            
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("💡 请确保设置了GOOGLE_API_KEY环境变量")
        print("   例如: export GOOGLE_API_KEY='your_api_key_here'")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
