import os
import yaml
from quivr_core import Brain
from quivr_core.llm.llm_endpoint import LLMEndpoint
from quivr_core.rag.entities.config import LLMEndpointConfig, DefaultModelSuppliers

def load_gemini_config(config_path: str = "gemini_config.yaml"):
    """从YAML文件加载Gemini配置"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except FileNotFoundError:
        print(f"配置文件 {config_path} 未找到")
        return None
    except yaml.YAMLError as e:
        print(f"解析YAML配置文件时出错: {e}")
        return None

def create_gemini_brain_from_config(config_path: str = "gemini_config.yaml"):
    """从配置文件创建使用Gemini的Brain"""
    
    # 加载配置
    config = load_gemini_config(config_path)
    if not config:
        return None
    
    llm_config_data = config.get('llm_config', {})
    
    # 检查API密钥
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError("请设置GOOGLE_API_KEY环境变量")
    
    # 创建LLM配置
    llm_config = LLMEndpointConfig(
        supplier=DefaultModelSuppliers.GEMINI,
        model=llm_config_data.get('model', 'gemini-2.5'),
        llm_api_key=api_key,
        temperature=llm_config_data.get('temperature', 0.3),
        max_output_tokens=llm_config_data.get('max_output_tokens', 4096),
        max_context_tokens=llm_config_data.get('max_context_tokens', 128000)
    )
    
    # 创建LLM端点
    llm_endpoint = LLMEndpoint.from_config(llm_config)
    
    return llm_endpoint

def main():
    """主函数"""
    try:
        # 从配置文件创建LLM端点
        llm_endpoint = create_gemini_brain_from_config()
        if not llm_endpoint:
            return
        
        # 创建Brain（这里使用一个示例文档）
        brain = Brain.from_files(
            name="gemini_config_brain",
            file_paths=["README.md"],  # 使用项目的README文件
            llm=llm_endpoint,
        )
        
        print("🧠 使用配置文件的Gemini Brain创建成功!")
        brain.print_info()
        
        # 测试问答
        question = "这个项目是关于什么的？"
        print(f"\n❓ 问题: {question}")
        answer = brain.ask(question)
        print(f"🤖 回答: {answer.answer}")
        
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
